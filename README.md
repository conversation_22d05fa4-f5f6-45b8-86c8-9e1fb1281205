# 🛍️ Skeyy E-Commerce Platform

A comprehensive full-stack e-commerce platform with multi-role authentication, Google OAuth integration, and complete vendor management system.

## 🌟 Overview

Skeyy is a modern e-commerce platform that supports three distinct user roles:
- **👤 Customers**: Browse, search, and purchase products
- **🏪 Vendors**: Sell products, manage inventory, and handle orders
- **⚙️ Administrators**: Manage the entire platform, users, and vendors

## 🚀 Key Features

### 🔐 Authentication System
- **Traditional Login/Signup**: Email and password authentication
- **Google OAuth Integration**: One-click signup/login with role selection
- **JWT-based Security**: Secure token-based authentication
- **Role-based Access Control**: Different dashboards for each user type

### 🛒 Customer Features
- Product browsing and search
- Real-time search functionality
- Shopping cart management
- Wishlist functionality
- Order tracking and history
- Secure checkout process
- Address management

### 🏪 Vendor Features
- Vendor registration with business verification
- Product management (add, edit, delete)
- Inventory tracking
- Order management (fulfilled/unfulfilled)
- Sales analytics and reporting
- Coupon creation and management
- Profile management

### ⚙️ Admin Features
- User management and analytics
- Vendor approval and management
- Product oversight and moderation
- Order management across all vendors
- Platform analytics and reporting
- Coupon system management

## 🏗️ System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   (HTML/CSS/JS) │◄──►│   (Node.js)     │◄──►│   (MySQL)       │
│                 │    │   Express.js    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Technology Stack
- **Frontend**: HTML5, CSS3, JavaScript (ES6+), SCSS
- **Backend**: Node.js, Express.js
- **Database**: MySQL with Sequelize ORM
- **Authentication**: JWT, Google OAuth 2.0, Passport.js
- **File Upload**: Multer
- **Security**: bcryptjs, CORS

## 📁 Project Structure

```
skeyy-ecommerce/
├── frontend/           # Customer-facing web application
├── admin pannel/       # Administrator dashboard
├── vendor pannel/      # Vendor management system
├── backend/           # API server and business logic
├── passwords.md       # System credentials (development)
└── README.md         # This file
```

## 🔧 Installation & Setup

### Prerequisites
- Node.js (v14 or higher)
- MySQL (v8.0 or higher)
- Google Cloud Console account (for OAuth)

### 1. Clone Repository
```bash
git clone <repository-url>
cd skeyy-ecommerce
```

### 2. Backend Setup
```bash
cd backend
npm install
```

### 3. Database Configuration
1. Create MySQL database named `skey`
2. Update `.env` file with your database credentials
3. Run database migrations:
```bash
node scripts/addGoogleOAuthFields.js
```

### 4. Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create OAuth 2.0 credentials
3. Update `.env` with your Google credentials:
```env
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
```

### 5. Start the Application
```bash
# Start backend server
cd backend
npm run dev

# Serve frontend (use Live Server or similar)
# Open frontend/index.html in browser
```

## 🌐 Application URLs

- **Customer Portal**: `frontend/index.html`
- **Vendor Panel**: `vendor pannel/index.html`
- **Admin Panel**: `admin pannel/index.html`
- **API Server**: `http://localhost:5000`

## 🔑 Authentication Flow

### Traditional Authentication
1. User registers with email/password
2. System generates JWT token
3. Token used for subsequent API calls
4. Role-based redirection after login

### Google OAuth Flow
1. User clicks "Sign in with Google"
2. Google authentication popup
3. For new users: Role selection modal appears
4. Account created with selected role
5. JWT token generated and stored
6. Redirection based on role:
   - Customer → Home page
   - Vendor → Vendor registration (if new) or dashboard
   - Admin → Not allowed via Google OAuth

## 📊 Database Schema

### Core Tables
- `user`: User accounts and authentication
- `vendorProfile`: Vendor business information
- `product`: Product catalog
- `category`/`subcategory`: Product categorization
- `cart`: Shopping cart items
- `order`/`orderItem`: Order management
- `wishlist`: User wishlists
- `coupon`: Discount coupons

## 🛡️ Security Features

- **Password Hashing**: bcryptjs with salt rounds
- **JWT Tokens**: Secure authentication tokens
- **Role-based Access**: Middleware protection
- **Input Validation**: Server-side validation
- **CORS Protection**: Cross-origin request security
- **Google OAuth**: Secure third-party authentication

## 🚀 Deployment

### Environment Variables
```env
DB_NAME=skey
DB_USER=root
DB_PASSWORD=your_password
DB_HOST=localhost
JWT_SECRET=your_jwt_secret
PORT=5000
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
```

### Production Considerations
- Use environment-specific configurations
- Enable HTTPS for production
- Set up proper database backups
- Configure proper CORS origins
- Use production-grade web server (nginx)

## 📚 Documentation & Repositories

### 📖 Documentation Links
- [Frontend Documentation](frontend/README.md)
- [Backend API Documentation](backend/README.md)
- [Admin Panel Documentation](admin%20pannel/README.md)
- [Vendor Panel Documentation](vendor%20pannel/README.md)

### 🔗 GitHub Repositories
- **Backend API**: [https://github.com/Bisam-27/skey-backend](https://github.com/Bisam-27/skey-backend)
- **Frontend Portal**: [https://github.com/Bisam-27/skey-frontend](https://github.com/Bisam-27/skey-frontend)
- **Admin Panel**: [https://github.com/Bisam-27/skey-adminpannel](https://github.com/Bisam-27/skey-adminpannel)
- **Vendor Panel**: [https://github.com/Bisam-27/skey-vendorpannel](https://github.com/Bisam-27/skey-vendorpannel)

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation in each module

---

**Built with ❤️ by the Skeyy Team**
