/* Authentication Form Styles */

/* Error and Success Messages */
.input__error {
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

.input__control--error {
  border-color: #e74c3c !important;
  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.1);
}

.login__error,
.signup__error {
  background-color: #fdf2f2;
  border: 1px solid #e74c3c;
  color: #e74c3c;
  padding: 1rem;
  border-radius: 4px;
  margin-top: 1rem;
  text-align: center;
}

.login__success,
.signup__success {
  background-color: #f0f9ff;
  border: 1px solid #10b981;
  color: #10b981;
  padding: 1rem;
  border-radius: 4px;
  margin-top: 1rem;
  text-align: center;
}

/* Loading States */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* User Navigation Dropdown */
.nav__user-dropdown {
  position: relative;
  display: inline-block;
}

.nav__user-btn {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.nav__user-btn:hover {
  background-color: #f5f5f5;
  border-radius: 8px;
}

.nav__user-menu {
  position: absolute;
  top: 25px;
  left: 0;
  
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
  min-width: 160px;
  z-index: 1000;
  display: none;
  margin-top: 0.5rem;
  overflow: hidden;
}

.nav__user-dropdown:hover .nav__user-menu {
  display: block;
  animation: fadeInDown 0.2s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.nav__user-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  color: #374151;
  text-decoration: none;
  transition: background-color 0.2s;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.nav__menu-icon {
  width: 16px;
  height: 16px;
  opacity: 0.7;
}

.nav__user-link:hover {
  background-color: #f5f5f5;
}

.nav__logout-btn {
  border-top: 2px solid #e5e7eb;
  color: #dc2626;
}

.nav__logout-btn:hover {
  background-color: #fef2f2;
  color: #b91c1c;
}

/* Form Validation Styles */
.input__control:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.input__control:invalid {
  border-color: #e74c3c;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav__user-menu {
    right: -50px;
    min-width: 120px;
  }
  
  .nav__user-btn span {
    display: none;
  }
}

/* Loading Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

/* User Type Toggle Styles */
.signup__user-type {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.signup__user-type-title {
  font-weight: 600;
  margin-bottom: 12px;
  color: #495057;
  font-size: 14px;
}

.signup__user-type-options {
  display: flex;
  gap: 20px;
}

.signup__user-type-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.signup__user-type-option:hover {
  background-color: #e9ecef;
}

.signup__user-type-radio {
  margin-right: 8px;
  accent-color: #007bff;
}

.signup__user-type-label {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

/* Customer Fields Styles */
.signup__customer-fields {
  margin-top: 16px;
}

/* Google OAuth Styles */
.login__divider,
.signup__divider {
  display: flex;
  align-items: center;
  margin: 24px 0;
  text-align: center;
}

.login__divider::before,
.login__divider::after,
.signup__divider::before,
.signup__divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: #e5e7eb;
}

.login__divider span,
.signup__divider span {
  padding: 0 16px;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.login__google-wrapper,
.signup__google-wrapper {
  margin-bottom: 24px;
}

.login__google-wrapper div,
.signup__google-wrapper div {
  display: flex;
  justify-content: center;
  width: 100%;
}

/* Custom Google button styling if needed */
.google-signin-button {
  width: 100% !important;
  height: 48px !important;
  border-radius: 8px !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .signup__user-type-options {
    flex-direction: column;
    gap: 12px;
  }

  .signup__vendor-fields {
    padding: 16px;
  }

  .login__divider,
  .signup__divider {
    margin: 20px 0;
  }
}
