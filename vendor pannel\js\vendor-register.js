// Vendor Registration Form Handler
document.addEventListener('DOMContentLoaded', function() {
  // Check if vendor is already logged in
  if (vendorAuthService.isAuthenticated() && vendorAuthService.isVendor()) {
    vendorAuthService.redirectToVendorDashboard();
    return;
  }

  // Check for Google OAuth completion
  checkGoogleOAuthCompletion();

  // Initialize registration form
  initializeVendorRegistrationForm();
});

// Check if user came from Google OAuth vendor selection
function checkGoogleOAuthCompletion() {
  const googleToken = sessionStorage.getItem('googleToken');
  const pendingRole = sessionStorage.getItem('pendingRole');

  if (googleToken && pendingRole === 'vendor') {
    // Show Google OAuth completion message
    showGoogleOAuthMessage();

    // Pre-fill email if available from Google
    prefilGoogleUserData(googleToken);
  }
}

// Show message that user needs to complete vendor registration
function showGoogleOAuthMessage() {
  const header = document.querySelector('.register__header .container');
  if (header) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'google-oauth-message';
    messageDiv.innerHTML = `
      <div style="background: #e3f2fd; border: 1px solid #2196f3; color: #1565c0; padding: 16px; border-radius: 8px; margin: 16px 0;">
        <h4 style="margin: 0 0 8px 0;">🎉 Google Account Connected!</h4>
        <p style="margin: 0;">Please complete your vendor registration below to start selling on sKeyy.</p>
      </div>
    `;
    header.appendChild(messageDiv);
  }
}

// Pre-fill form with Google user data
async function prefilGoogleUserData(googleToken) {
  try {
    // Decode Google token to get user info (basic client-side decode)
    const payload = JSON.parse(atob(googleToken.split('.')[1]));

    // Pre-fill email
    const emailInput = document.querySelector('#js-email');
    if (emailInput && payload.email) {
      emailInput.value = payload.email;
      emailInput.readOnly = true; // Make email read-only since it's from Google
      emailInput.style.backgroundColor = '#f5f5f5'; // Visual indication
    }

    // Pre-fill contact name if available
    const contactNameInput = document.querySelector('#js-contact-name');
    if (contactNameInput && payload.name) {
      contactNameInput.value = payload.name;
    }

    // Hide password fields for Google OAuth users
    hidePasswordFields();

  } catch (error) {
    console.error('Error pre-filling Google data:', error);
  }
}

// Hide password fields for Google OAuth users
function hidePasswordFields() {
  const passwordSection = document.querySelector('#js-password')?.closest('.input');
  const confirmPasswordSection = document.querySelector('#js-confirm-password')?.closest('.input');

  if (passwordSection) {
    passwordSection.style.display = 'none';
  }

  if (confirmPasswordSection) {
    confirmPasswordSection.style.display = 'none';
  }

  // Add a note about Google authentication
  const emailSection = document.querySelector('#js-email')?.closest('.input');
  if (emailSection) {
    const note = document.createElement('div');
    note.style.cssText = 'color: #666; font-size: 12px; margin-top: 4px;';
    note.textContent = 'Authentication handled by Google - no password required';
    emailSection.appendChild(note);
  }
}

function initializeVendorRegistrationForm() {
  const registerForm = document.querySelector('.register__form');
  if (!registerForm) return;

  // Handle form submission
  registerForm.addEventListener('submit', async function(e) {
    e.preventDefault();

    // Get form inputs
    const businessNameInput = document.querySelector('#js-business-name');
    const contactNameInput = document.querySelector('#js-contact-name');
    const mobileNumberInput = document.querySelector('#js-mobile-number');
    const gstNumberInput = document.querySelector('#js-gst-number');
    const addressInput = document.querySelector('#js-address');
    const emailInput = document.querySelector('#js-email');
    const passwordInput = document.querySelector('#js-password');
    const confirmPasswordInput = document.querySelector('#js-confirm-password');
    const bankNameInput = document.querySelector('#js-bank-name');
    const panInput = document.querySelector('#js-pan');
    const submitBtn = document.querySelector('#js-open-modal');

    // Clear previous errors
    FormValidator.clearAllErrors(registerForm);

    // Get form values
    const formData = {
      businessName: businessNameInput.value.trim(),
      contactName: contactNameInput.value.trim(),
      mobileNumber: mobileNumberInput.value.trim(),
      gstNumber: gstNumberInput.value.trim(),
      businessAddress: addressInput.value.trim(),
      email: emailInput.value.trim(),
      password: passwordInput.value,
      confirmPassword: confirmPasswordInput.value,
      bankName: bankNameInput.value.trim(),
      panNumber: panInput.value.trim()
    };

    // Check if this is a Google OAuth completion
    const googleToken = sessionStorage.getItem('googleToken');
    const pendingRole = sessionStorage.getItem('pendingRole');
    const isGoogleOAuth = googleToken && pendingRole === 'vendor';

    // Validation
    let hasErrors = false;

    // Required fields validation
    if (!formData.businessName) {
      FormValidator.showError(businessNameInput, 'Business name is required');
      hasErrors = true;
    }

    if (!formData.contactName) {
      FormValidator.showError(contactNameInput, 'Contact name is required');
      hasErrors = true;
    }

    if (!formData.mobileNumber) {
      FormValidator.showError(mobileNumberInput, 'Mobile number is required');
      hasErrors = true;
    } else if (!FormValidator.validatePhone(formData.mobileNumber)) {
      FormValidator.showError(mobileNumberInput, 'Please enter a valid mobile number');
      hasErrors = true;
    }

    if (!formData.email) {
      FormValidator.showError(emailInput, 'Email is required');
      hasErrors = true;
    } else if (!FormValidator.validateEmail(formData.email)) {
      FormValidator.showError(emailInput, 'Please enter a valid email');
      hasErrors = true;
    }

    // Password validation - skip for Google OAuth users
    if (!isGoogleOAuth) {
      if (!formData.password) {
        FormValidator.showError(passwordInput, 'Password is required');
        hasErrors = true;
      } else if (formData.password.length < 6) {
        FormValidator.showError(passwordInput, 'Password must be at least 6 characters long');
        hasErrors = true;
      }

      if (!formData.confirmPassword) {
        FormValidator.showError(confirmPasswordInput, 'Please confirm your password');
        hasErrors = true;
      } else if (formData.password !== formData.confirmPassword) {
        FormValidator.showError(confirmPasswordInput, 'Passwords do not match');
        hasErrors = true;
      }
    }

    if (hasErrors) return;

    // Show loading state
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Creating Account...';
    submitBtn.disabled = true;

    try {
      let registrationResult;

      if (isGoogleOAuth) {
        // Complete Google OAuth vendor registration
        registrationResult = await completeGoogleOAuthVendorRegistration(googleToken, formData);

        // Clear session storage
        sessionStorage.removeItem('googleToken');
        sessionStorage.removeItem('pendingRole');
      } else {
        // Regular vendor registration
        registrationResult = await vendorAuthService.register(formData);
      }

      // Show success message
      const successDiv = document.createElement('div');
      successDiv.className = 'register__success';
      successDiv.style.cssText = `
        background-color: #d4edda;
        color: #155724;
        padding: 16px;
        border-radius: 4px;
        margin: 20px 0;
        text-align: center;
        border: 1px solid #c3e6cb;
        font-weight: bold;
      `;
      successDiv.innerHTML = `
        <h3>Registration Successful!</h3>
        <p>Your vendor account has been created successfully.</p>
        <p>Redirecting to vendor dashboard...</p>
      `;
      
      // Insert success message at the top of the form
      registerForm.insertBefore(successDiv, registerForm.firstChild);

      // Scroll to top to show success message
      window.scrollTo({ top: 0, behavior: 'smooth' });

      // Redirect to vendor dashboard after delay
      setTimeout(() => {
        vendorAuthService.redirectToVendorDashboard();
      }, 2000);

    } catch (error) {
      // Show error message
      const errorDiv = document.createElement('div');
      errorDiv.className = 'register__error';
      errorDiv.style.cssText = `
        background-color: #f8d7da;
        color: #721c24;
        padding: 16px;
        border-radius: 4px;
        margin: 20px 0;
        text-align: center;
        border: 1px solid #f5c6cb;
        font-weight: bold;
      `;
      errorDiv.innerHTML = `
        <h3>Registration Failed</h3>
        <p>${error.message}</p>
      `;
      
      // Insert error message at the top of the form
      registerForm.insertBefore(errorDiv, registerForm.firstChild);

      // Scroll to top to show error message
      window.scrollTo({ top: 0, behavior: 'smooth' });

    } finally {
      // Reset button
      submitBtn.textContent = originalText;
      submitBtn.disabled = false;
    }
  });

  // Clear errors on input
  const inputs = registerForm.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('input', function() {
      FormValidator.clearError(this);
    });
  });

  // Handle modal functionality (for future OTP implementation)
  const modal = document.querySelector('.modal');
  const modalCloseBtn = document.querySelector('.modal__btn');
  const modalBg = document.querySelector('.modal__bg');

  if (modalCloseBtn) {
    modalCloseBtn.addEventListener('click', function() {
      modal.style.display = 'none';
    });
  }

  if (modalBg) {
    modalBg.addEventListener('click', function() {
      modal.style.display = 'none';
    });
  }
}

// Complete Google OAuth vendor registration
async function completeGoogleOAuthVendorRegistration(googleToken, vendorData) {
  try {
    const response = await fetch('http://localhost:5000/api/auth/google', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: googleToken,
        role: 'vendor',
        vendorProfile: {
          businessName: vendorData.businessName,
          contactName: vendorData.contactName,
          mobileNumber: vendorData.mobileNumber,
          gstNumber: vendorData.gstNumber,
          businessAddress: vendorData.businessAddress,
          bankName: vendorData.bankName,
          panNumber: vendorData.panNumber
        }
      })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Google OAuth vendor registration failed');
    }

    // Store auth data using the main auth service
    if (typeof authService !== 'undefined') {
      authService.setAuthData(data.data.token, data.data.user);
    } else {
      // Fallback to localStorage
      localStorage.setItem('skeyy_auth_token', data.data.token);
      localStorage.setItem('skeyy_user_data', JSON.stringify(data.data.user));
    }

    return data;
  } catch (error) {
    console.error('Google OAuth vendor registration error:', error);
    throw error;
  }
}
