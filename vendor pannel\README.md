# 🏪 Skeyy Vendor Panel

The vendor management dashboard for the Skeyy e-commerce platform, enabling businesses to manage their products, orders, and analytics with Google OAuth integration.

## 🔗 Related Repositories

- **Vendor Panel**: [https://github.com/Bisam-27/skey-vendorpannel](https://github.com/Bisam-27/skey-vendorpannel) (This Repository)
- **Backend API**: [https://github.com/Bisam-27/skey-backend](https://github.com/Bisam-27/skey-backend)
- **Frontend Portal**: [https://github.com/Bisam-27/skey-frontend](https://github.com/Bisam-27/skey-frontend)
- **Admin Panel**: [https://github.com/Bisam-27/skey-adminpannel](https://github.com/Bisam-27/skey-adminpannel)

## 📚 Documentation Links

- [Backend API Documentation](https://github.com/Bisam-27/skey-backend#readme)
- [Frontend Documentation](https://github.com/Bisam-27/skey-frontend#readme)
- [Admin Panel Documentation](https://github.com/Bisam-27/skey-adminpannel#readme)

## 🌟 Features

### 🔐 Vendor Authentication
- **Traditional Registration**: Business verification with detailed forms
- **Google OAuth Integration**: Quick signup with business details completion
- **JWT Token Management**: Secure vendor session handling
- **Business Profile**: Complete vendor profile management
- **Multi-step Verification**: Business details, GST, banking information

### 📦 Product Management
- **Product Creation**: Add new products with images and details
- **Inventory Management**: Track stock levels and availability
- **Product Updates**: Edit existing product information
- **Image Upload**: Multiple product images with optimization
- **Category Assignment**: Organize products by categories
- **Pricing Control**: Set prices and discounts

### 📊 Order Management
- **Order Dashboard**: View all vendor-specific orders
- **Order Fulfillment**: Mark orders as fulfilled
- **Order History**: Track completed orders
- **Customer Information**: Access customer details for orders
- **Order Analytics**: Sales performance metrics

### 💰 Business Analytics
- **Sales Dashboard**: Revenue and sales statistics
- **Top Products**: Best-selling product analytics
- **Order Trends**: Historical order data
- **Performance Metrics**: Business growth indicators

### 🎫 Coupon Management
- **Create Coupons**: Vendor-specific discount codes
- **Coupon Analytics**: Track coupon usage
- **Expiry Management**: Set coupon validity periods
- **Usage Limits**: Control coupon redemption limits

## 🛠️ Technology Stack

- **Frontend**: HTML5, CSS3/SCSS, Vanilla JavaScript
- **Authentication**: JWT, Google OAuth 2.0
- **File Upload**: Multer integration for product images
- **Charts**: Chart.js for analytics visualization
- **Responsive Design**: Mobile-friendly vendor dashboard
- **API Integration**: RESTful backend communication

## 📁 File Structure

```
vendor pannel/
├── index.html              # Vendor dashboard homepage
├── register.html           # Vendor registration with Google OAuth
├── login.html             # Vendor login page
├── products.html          # Product management
├── add-product.html       # Add new product
├── edit-product.html      # Edit existing product
├── orders.html            # Order management
├── coupons.html           # Coupon management
├── profile.html           # Vendor profile management
├── analytics.html         # Business analytics dashboard
├── css/
│   ├── vendor-main.css    # Main vendor panel styles
│   ├── vendor-auth.css    # Authentication styles
│   ├── vendor-dashboard.css # Dashboard-specific styles
│   └── vendor-responsive.css # Mobile responsiveness
├── js/
│   ├── vendor-auth.js     # Vendor authentication
│   ├── vendor-register.js # Registration with Google OAuth
│   ├── vendor-dashboard.js # Dashboard functionality
│   ├── vendor-products.js # Product management
│   ├── vendor-orders.js   # Order management
│   ├── vendor-coupons.js  # Coupon management
│   ├── vendor-profile.js  # Profile management
│   ├── vendor-analytics.js # Analytics and charts
│   └── vendor-utils.js    # Utility functions
├── img/                   # Vendor panel images
└── uploads/               # Uploaded product images
```

## 🔐 Authentication Flow

### Traditional Vendor Registration
1. **Business Information**: Company details, contact info
2. **Verification Details**: GST number, PAN, banking info
3. **Account Creation**: Email/password setup
4. **Profile Completion**: Additional business details
5. **Dashboard Access**: Full vendor panel access

### Google OAuth Vendor Registration
1. **Google Authentication**: User selects "Vendor" role
2. **Redirect to Registration**: Automatic redirect to vendor registration
3. **Pre-filled Information**: Email and name from Google
4. **Business Details**: Complete business verification
5. **Account Linking**: Google account linked with vendor profile
6. **Dashboard Access**: Immediate access to vendor panel

### Security Features
- **Business Verification**: Required business documentation
- **Role Isolation**: Vendors can only access their own data
- **JWT Protection**: Secure API communication
- **File Upload Security**: Validated image uploads
- **Session Management**: Automatic logout on inactivity

## 🏪 Vendor Dashboard Features

### Dashboard Overview
- **Sales Summary**: Today's sales, total revenue
- **Order Statistics**: Pending, fulfilled, total orders
- **Product Count**: Total products, low stock alerts
- **Recent Activity**: Latest orders and updates
- **Quick Actions**: Add product, view orders shortcuts

### Product Management
- **Product Listing**: Grid view of all vendor products
- **Add New Product**: Comprehensive product creation form
- **Edit Products**: Update existing product information
- **Image Management**: Upload and manage product images
- **Stock Tracking**: Monitor inventory levels
- **Category Assignment**: Organize products by categories

### Order Processing
- **Order Queue**: Pending orders requiring fulfillment
- **Order Details**: Complete order information
- **Customer Info**: Shipping addresses and contact details
- **Fulfillment Actions**: Mark orders as completed
- **Order History**: Archive of completed orders
- **Order Search**: Find specific orders quickly

### Analytics Dashboard
- **Revenue Charts**: Daily, weekly, monthly sales
- **Product Performance**: Top-selling products
- **Order Trends**: Historical order patterns
- **Customer Analytics**: Repeat customer insights
- **Growth Metrics**: Business performance indicators

## 🔧 API Integration

### Vendor-Specific Endpoints
```javascript
// Authentication
POST /api/vendor/register    # Vendor registration
POST /api/auth/google       # Google OAuth with vendor role

// Products (Vendor-specific)
GET /api/vendor/products    # Get vendor's products only
POST /api/vendor/products   # Create new product
PUT /api/vendor/products/:id # Update own product
DELETE /api/vendor/products/:id # Delete own product

// Orders (Vendor-specific)
GET /api/vendor/orders      # Get vendor's orders only
GET /api/vendor/orders/fulfilled # Fulfilled orders
GET /api/vendor/orders/unfulfilled # Pending orders
PUT /api/vendor/orders/:id/fulfill # Mark order fulfilled

// Coupons (Vendor-specific)
GET /api/vendor/coupons     # Get vendor's coupons
POST /api/vendor/coupons    # Create vendor coupon
PUT /api/vendor/coupons/:id # Update own coupon
DELETE /api/vendor/coupons/:id # Delete own coupon

// Analytics
GET /api/vendor/analytics/sales # Sales data
GET /api/vendor/analytics/products # Product performance
GET /api/vendor/analytics/orders # Order statistics
```

### Data Isolation
- **Vendor-specific Data**: Each vendor sees only their own data
- **Product Ownership**: Vendors can only manage their products
- **Order Access**: Limited to orders containing vendor's products
- **Coupon Scope**: Vendor-created coupons only
- **Analytics Privacy**: Business metrics kept private

## 🎨 User Interface

### Design System
- **Color Scheme**: Professional business-focused palette
- **Typography**: Clear, readable fonts for data-heavy interfaces
- **Icons**: Intuitive icons for quick recognition
- **Layout**: Clean, organized dashboard layout
- **Responsive**: Mobile-friendly for on-the-go management

### Key Components
- **Navigation Sidebar**: Quick access to all features
- **Data Tables**: Sortable, filterable product and order lists
- **Forms**: User-friendly forms with validation
- **Charts**: Visual analytics with Chart.js
- **Modals**: Overlay dialogs for actions
- **Toast Notifications**: Success/error feedback

### Mobile Optimization
- **Responsive Tables**: Mobile-friendly data display
- **Touch-friendly**: Large buttons and touch targets
- **Collapsible Sidebar**: Space-efficient navigation
- **Optimized Forms**: Mobile-friendly input fields

## 🚀 Setup & Development

### Prerequisites
- Backend API server running
- Google OAuth credentials configured
- Modern web browser
- File upload permissions

### Local Development
1. **Start Backend Server**
   ```bash
   cd backend
   npm run dev
   ```

2. **Configure Google OAuth**
   - Update Google Client ID in `js/vendor-register.js`
   - Ensure vendor registration redirect is configured

3. **Serve Vendor Panel**
   - Use Live Server or local HTTP server
   - Access via `vendor pannel/index.html`

### Environment Configuration
```javascript
// Update in vendor JavaScript files
const API_BASE_URL = 'http://localhost:5000/api';
const GOOGLE_CLIENT_ID = 'your_google_client_id';
```

## 📊 Analytics & Reporting

### Sales Analytics
- **Revenue Tracking**: Daily, weekly, monthly sales
- **Product Performance**: Best and worst-selling products
- **Order Volume**: Order count trends
- **Average Order Value**: Customer spending patterns

### Business Insights
- **Customer Behavior**: Repeat purchase patterns
- **Seasonal Trends**: Sales seasonality analysis
- **Product Categories**: Category performance comparison
- **Growth Metrics**: Month-over-month growth

### Export Features
- **Sales Reports**: Downloadable sales data
- **Product Reports**: Inventory and performance reports
- **Order Reports**: Fulfillment and shipping reports
- **Tax Reports**: GST and tax calculation reports

## 🔒 Security & Compliance

### Business Security
- **Data Encryption**: Sensitive business data encrypted
- **Access Control**: Role-based permissions
- **Audit Trails**: Activity logging for compliance
- **Secure File Upload**: Validated image uploads
- **Session Security**: Automatic timeout and logout

### Compliance Features
- **GST Integration**: Tax calculation and reporting
- **Business Verification**: Required documentation
- **Data Privacy**: GDPR-compliant data handling
- **Financial Security**: Secure payment information

## 🐛 Troubleshooting

### Common Issues

1. **Google OAuth Registration**
   - Verify Google credentials are configured
   - Check redirect URLs in Google Console
   - Ensure vendor registration page is accessible

2. **Product Upload Issues**
   - Check file size limits
   - Verify image format support
   - Ensure upload directory permissions

3. **Order Management**
   - Verify vendor authentication
   - Check order data isolation
   - Ensure proper API endpoints

4. **Analytics Not Loading**
   - Check Chart.js library loading
   - Verify API data format
   - Ensure proper date ranges

### Debug Tools
- Browser Developer Tools
- Network tab for API requests
- Console for JavaScript errors
- Application tab for token storage

## 📈 Performance Optimization

### Loading Performance
- **Lazy Loading**: Charts and images loaded on demand
- **Minified Assets**: Compressed CSS and JavaScript
- **Efficient Queries**: Optimized API calls
- **Caching**: Local storage for frequently accessed data

### User Experience
- **Fast Navigation**: Quick page transitions
- **Real-time Updates**: Live order notifications
- **Bulk Operations**: Batch product updates
- **Keyboard Shortcuts**: Power user features

---

**🏪 Skeyy Vendor Panel - Empowering Business Success**
