{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "node server.js", "setup:coupons": "node scripts/createCouponTables.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.11.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "google-auth-library": "^10.2.1", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "mysql2": "^3.14.2", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "sequelize": "^6.37.7"}}